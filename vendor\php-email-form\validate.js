(function () {
  "use strict";

  let forms = document.querySelectorAll('.php-email-form');

  forms.forEach(function (e) {
    e.addEventListener('submit', function (event) {
      event.preventDefault();
      if (this.classList.contains('submitted')) return;
      this.classList.add('submitted');

      let thisForm = this;
      let submitBtn = thisForm.querySelector('button[type="submit"]');

      let action = thisForm.getAttribute('action');
      if (!action) return;

      submitBtn.disabled = true;
      thisForm.querySelector('.loading').classList.add('d-block');
      thisForm.querySelector('.error-message').classList.remove('d-block');
      thisForm.querySelector('.sent-message').classList.remove('d-block');

      let formData = new FormData(thisForm);

      const afterSubmitCleanup = () => {
        thisForm.classList.remove('submitted');
        $(thisForm).find("input[type=text], input[type=email], textarea").val("");
        submitBtn.disabled = false;
        document.getElementById("submitText").textContent = "Send Message";
        document.getElementById("spinner").classList.add("d-none");
      };

      fetch(action, {
        method: 'POST',
        body: formData,
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
      })
        .then(response => response.text())
        .then(data => {
          thisForm.querySelector('.loading').classList.remove('d-block');
          if (data.trim().toLowerCase().includes('ok')) {
            thisForm.querySelector('.sent-message').classList.add('d-block');
            $('#successModal').modal('show');
            $('.select2').val(null).trigger('change');
                    $(".php-email-form")[0].reset();
            afterSubmitCleanup();
          } else {
            throw new Error(data || 'Form submission failed');
          }
        })
        .catch((error) => {
          thisForm.querySelector('.loading').classList.remove('d-block');
          thisForm.querySelector('.error-message').innerHTML = error;
          thisForm.querySelector('.error-message').classList.add('d-block');
          afterSubmitCleanup();
        });
    });
  });
})();
