<?php
// Immediately return <PERSON><PERSON><PERSON> to the client
header('Content-Type: application/json');
echo json_encode(['status' => 'success', 'message' => 'OK']);

// Clear the file first
//file_put_contents('email_log.txt', '');
// Then append your message
file_put_contents('email_log.txt', date('Y-m-d H:i:s') . " - Form Submitted " . "\n", FILE_APPEND);


// Trigger background email script
$payload = $_POST;
$ch = curl_init('https://lionsclub.zlendo.com/forms/contact-mail.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($payload));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 1); // Important: fast timeout
curl_exec($ch);
curl_close($ch);

// Done
exit;
