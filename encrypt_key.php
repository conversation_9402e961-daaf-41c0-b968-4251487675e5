<?php
$key = "b618391a-ad46-4a0f-8fdd-7ef69863c6cb"; // Your Web3Forms key
$encryption_key = "T@nz33lP0st_Key_2025";     // Must match submit.php
$iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length('aes-256-cbc'));

$encrypted = openssl_encrypt($key, 'aes-256-cbc', $encryption_key, 0, $iv);

echo "Encrypted Key: " . base64_encode($encrypted) . PHP_EOL;
echo "IV: " . base64_encode($iv) . PHP_EOL;
?>
