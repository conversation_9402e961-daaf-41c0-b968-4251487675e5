<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_189_1113)">
<path d="M13 17.48C13 13.9012 15.9012 11 19.48 11H60.52C64.0988 11 67 13.9012 67 17.48V58.52C67 62.0988 64.0988 65 60.52 65H19.48C15.9012 65 13 62.0988 13 58.52V17.48Z" fill="white" shape-rendering="crispEdges"/>
<g clip-path="url(#clip0_189_1113)">
<path d="M55.1199 48.8002V50.9602H25.9599L24.8799 49.8802V20.7202H27.0399V48.8002H55.1199Z" fill="#FF4500"/>
<path d="M50.8 27.6472L39.6846 38.7647H38.1553L34.6 35.2072L26.7246 43.0847L25.1953 41.5555L33.8353 32.9155H35.3646L38.92 36.473L50.0353 25.3555H51.5624L55.8824 29.6755L54.3553 31.2047L50.8 27.6472Z" fill="#FF4500"/>
</g>
</g>
<defs>
<filter id="filter0_d_189_1113" x="0.579999" y="0.739999" width="78.84" height="78.84" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.16"/>
<feGaussianBlur stdDeviation="6.21"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.270588 0 0 0 0 0 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_189_1113"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_189_1113" result="shape"/>
</filter>
<clipPath id="clip0_189_1113">
<rect width="34.56" height="34.56" fill="white" transform="translate(22.72 20.7202)"/>
</clipPath>
</defs>
</svg>
