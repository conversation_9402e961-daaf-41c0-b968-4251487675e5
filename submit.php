<?php
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // Encrypted access key details
    $encrypted_key_base64 = "SS9OODdwcUhwOEFUSU1Qd01oUVdZUmFvK1dLQm5pOFptdStWanFxSXZOUmRVeVZGVjY0dGpDZjhWaFQrSVNZZg==";
$iv_base64 = "lk+vEXc0asZufYqtpeiEWg==";
$encryption_key = "T@nz33lP0st_Key_2025";


    // Decrypt the Web3Forms access key
    $encrypted_key = base64_decode($encrypted_key_base64);
    $iv = base64_decode($iv_base64);
    $access_key = openssl_decrypt($encrypted_key, 'aes-256-cbc', $encryption_key, 0, $iv);

    $name = htmlspecialchars($_POST['name'] ?? '');
    $email = htmlspecialchars($_POST['email'] ?? '');
    $message = htmlspecialchars($_POST['message'] ?? '');
      $replyto = htmlspecialchars($_POST['replyto'] ?? '');

    $data = [
        "access_key" => $access_key,
        "name" => $name,
        "email" => $email,
        "message" => $message,
        "replyto" => $replyto,
    ];

    $ch = curl_init("https://api.web3forms.com/submit");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    $response = curl_exec($ch);
    curl_close($ch);

    $result = json_decode($response, true);

    if (isset($result['success']) && $result['success']) {
        echo "<h3>Thank you! Your message has been sent successfully.</h3>";
    } else {
        echo "<h3>Oops! Something went wrong.</h3>";
        echo "<pre>" . print_r($result, true) . "</pre>";
    }
} else {
    echo "<h3>Invalid request.</h3>";
}
?>
