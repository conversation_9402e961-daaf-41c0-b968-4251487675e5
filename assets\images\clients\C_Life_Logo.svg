<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 841.89 595.28">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-1, .cls-2, .cls-3, .cls-4, .cls-5 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: #320049;
      }

      .cls-3 {
        fill: #ffbc00;
      }

      .cls-4 {
        fill: url(#linear-gradient-2);
      }

      .cls-5 {
        fill: url(#linear-gradient-3);
      }
    </style>
    <linearGradient id="linear-gradient" x1="278.71" y1="288.34" x2="193.73" y2="288.34" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#d100e3"/>
      <stop offset="1" stop-color="#8d00c1"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="149.67" y1="316.56" x2="326.97" y2="316.56" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#6d0099"/>
      <stop offset="1" stop-color="#8d00c1"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="229.04" y1="300.38" x2="214.75" y2="284.55" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#e49219"/>
      <stop offset="1" stop-color="#e49219" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <g>
    <path class="cls-1" d="M279.78,242.3c-3.73,3.68-8.69,5.65-13.72,5.65-2.05,0-4.11-.32-6.11-.99-10.05-4.81-21.3-7.5-33.18-7.5-42.58,0-77.1,34.52-77.1,77.1,0,23.41,10.42,44.37,26.89,58.51-23.64-17.37-39.04-45.31-39.22-76.89-.3-53.03,42.45-96.26,95.48-96.56,14.89-.08,29.02,3.23,41.63,9.21,12.21,5.79,14.95,21.97,5.33,31.48Z"/>
    <path class="cls-4" d="M326.34,321.73c-10.64,41.17-47.92,71.68-92.45,71.93-1.37,0-2.74-.02-4.08-.06-19.88-.72-38.23-7.49-53.25-18.53-16.46-14.14-26.89-35.1-26.89-58.51,0-42.58,34.52-77.1,77.1-77.1,11.88,0,23.14,2.69,33.18,7.5-.72-.25-1.44-.53-2.15-.87-7.5-3.57-15.9-5.54-24.76-5.49-31.5.18-56.89,25.86-56.71,57.36.17,31.5,25.85,56.89,57.35,56.71,26.35-.14,48.43-18.14,54.84-42.45,1.77-6.71,6.83-11.88,13.2-13.78,1.73-.52,3.57-.8,5.45-.8h.57c12.6,0,21.75,11.9,18.6,24.09Z"/>
    <path class="cls-3" d="M343.91,236.82l-88.56,83.85-15.5,14.67c-4.64,4.38-11.95,4.18-16.34-.45l-14.57-15.39-15.95-16.85c-.18-.19-.35-.38-.52-.58-5.7-6.61-5.25-16.61,1.16-22.67,6.39-6.05,16.36-5.97,22.65.03.21.19.4.39.6.6l15.84,16.74,16.8-15.9,71.77-67.95c3.18-3.01,7.25-4.5,11.31-4.5,4.36,0,8.71,1.72,11.95,5.14,6.25,6.6,5.96,17.01-.64,23.26Z"/>
    <path class="cls-5" d="M249.54,280.89l-34.62,32.92c-3.3,3.14-8.53,3-11.66-.31l-10.27-10.85.03-1.1,23.26-22.12c.21.19.4.39.6.6l15.84,16.74,16.8-15.9.02.02Z"/>
  </g>
  <g>
    <g>
      <path class="cls-2" d="M431.82,330.47c-3.09,1.43-10.59,3.33-20.22,3.33-29.97,0-43.18-18.79-43.18-40.08,0-28.31,20.7-42.7,44.6-42.7,9.28,0,16.77,1.78,20.1,3.57l-4.16,16.89c-3.57-1.43-8.45-2.85-14.87-2.85-12.37,0-23.08,7.26-23.08,23.67,0,14.63,8.68,23.67,23.43,23.67,5.23,0,11.06-1.07,14.39-2.38l2.97,16.89Z"/>
      <path class="cls-2" d="M481.18,252.32h21.53v62.21h30.81v17.96h-52.33v-80.17Z"/>
      <path class="cls-2" d="M565.51,252.32v80.17h-21.53v-80.17h21.53Z"/>
      <path class="cls-2" d="M580.26,252.32h51.5v17.48h-29.97v14.87h28.19v17.37h-28.19v30.45h-21.53v-80.17Z"/>
      <path class="cls-2" d="M695.4,300.14h-28.78v14.75h32.23v17.6h-53.76v-80.17h52.1v17.48h-30.57v12.96h28.78v17.37Z"/>
    </g>
    <rect class="cls-2" x="439.53" y="283.74" width="31.83" height="17.36"/>
  </g>
</svg>