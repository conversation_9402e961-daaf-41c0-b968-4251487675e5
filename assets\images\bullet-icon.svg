<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_914_3117)">
<circle cx="14" cy="10" r="7" fill="url(#paint0_linear_914_3117)"/>
<circle cx="14" cy="10" r="6.17647" stroke="white" stroke-width="1.64706"/>
</g>
<defs>
<filter id="filter0_d_914_3117" x="0" y="0" width="28" height="28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_914_3117"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_914_3117" result="shape"/>
</filter>
<linearGradient id="paint0_linear_914_3117" x1="18.7222" y1="17" x2="-1.82595" y2="6.69901" gradientUnits="userSpaceOnUse">
<stop offset="0.298911" stop-color="#FF603A"/>
<stop offset="1" stop-color="#FDC091"/>
</linearGradient>
</defs>
</svg>
