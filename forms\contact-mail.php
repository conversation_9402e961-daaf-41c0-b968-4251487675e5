<?php
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(403);
    exit('Forbidden');
}

file_put_contents('email_log.txt', date('Y-m-d H:i:s') . " - <PERSON><PERSON> Trigger " . "\n", FILE_APPEND);

$receiving_email_address = '<EMAIL>'; 

// CAPTCHA Validation
$generatedCaptcha = isset($_POST['generated_captcha']) ? $_POST['generated_captcha'] : ''; 

$userCaptcha = isset($_POST['user_captcha']) ? $_POST['user_captcha'] : ''; 

if ($userCaptcha !== $generatedCaptcha) {
    file_put_contents('email_log.txt', date('Y-m-d H:i:s') . " - Invalid CAPTCHA\n", FILE_APPEND);
    exit('Invalid CAPTCHA, please try again.');
}



// Load PHP Email Form library
if (file_exists($php_email_form = '../vendor/php-email-form/php-email-form.php')) {
    include($php_email_form);
} else {
	file_put_contents('email_log.txt', date('c') . " - Failed to load library\n", FILE_APPEND);
    exit;
}

// SMTP Configuration
$smtp_config = [
    'host' => 'smtp.office365.com',
    'username' => '<EMAIL>',
    // 'password' => 'ZDP;}6S07Is]UqykndSJ',
    'password' => '#}JtW3xk-fWpJP',
    'port' => '587',
    'encryption' => 'tls'
];

// Capitalize name
function capitalizeName($name) {
    return ucwords(strtolower(trim($name)));
}

$name = isset($_POST['name']) ? capitalizeName($_POST['name']) : 'No Name Provided';

// Response initialization
$response = ['status' => 'error', 'message' => 'Initialization error'];

try {
    // Email to Company
    $contact = new PHP_Email_Form;
    $contact->ajax = false;
    $contact->to = $receiving_email_address;
    $contact->from_name = $name;
    $contact->from_email = $_POST['email'] ?? 'No Email Provided';
    $contact->subject = 'New Inquiry From Contact';
    $contact->smtp = $smtp_config;

    $interested_about = isset($_POST['interested_about']) ? implode(", ", $_POST['interested_about']) : 'None selected';

    $emailContent = '<!doctype html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>body{margin:0;padding:0;background-color:#f4f5f6;font-family:Helvetica,sans-serif}table{border-collapse:collapse;width:100%}.container{max-width:600px;margin:auto;background:#ffffff;border-radius:16px;border:1px solid #eaebed;padding:24px}.title{font-size:18px;font-weight:bold;margin-bottom:16px;text-align:center}.info-row td{padding:8px 0;vertical-align:top;font-size:16px}.message-box{background:#f2f2f2;border-left:4px solid #FF603E;padding:12px;font-style:italic;font-size:15px;margin-top:10px}.footer{font-size:12px;color:#777;text-align:center;margin-top:20px}</style></head><body><table role="presentation" class="container"><tr><td class="title">New Contact Submission – Zlendo Technologies</td></tr><tr><td><table width="100%"><tr class="info-row"><td width="30%"><strong>Name:</strong></td><td>' . htmlspecialchars($name) . '</td></tr><tr class="info-row"><td><strong>Phone:</strong></td><td>' . htmlspecialchars($_POST['phone']) . '</td></tr><tr class="info-row"><td><strong>Email:</strong></td><td>' . htmlspecialchars($_POST['email']) . '</td></tr><tr class="info-row"><td><strong>Interested About:</strong></td><td>' . $interested_about . '</td></tr><tr class="info-row"><td colspan="2"><strong>Message:</strong><div class="message-box">' . nl2br(htmlspecialchars($_POST['message'])) . '</div></td></tr></table></td></tr><tr><td class="footer">Zlendo – AI-Powered Business Solutions</td></tr></table></body></html>';

    $contact->add_message($emailContent, '');
    $contact->is_html = true;

    $contact_result = $contact->send();
	
    if ($contact_result !== 'OK') {
	    throw new Exception('Failed to send company notification');
    } else {
		file_put_contents('email_log.txt', date('Y-m-d H:i:s') . " - " . "First Email" . ": " . $contact_result . "\n", FILE_APPEND);
	}

    // Email to User (Thank You)
    $usermail = new PHP_Email_Form;
    $usermail->ajax = false;
    $usermail->to = $_POST['email'];
    $usermail->from_name = 'Zlendo';
    $usermail->from_email = $receiving_email_address;
    $usermail->subject = 'Thank you for reaching out to Zlendo Technologies Private Limited!';
    $usermail->smtp = $smtp_config;

    $thankYouContent = '<!doctype html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>body{margin:0;padding:0;background-color:#f4f5f6;font-family:Helvetica,sans-serif}table{border-collapse:collapse;width:100%}.container{max-width:600px;margin:auto;background:linear-gradient(193deg,#FFF6EE -9.56%,#F3EFEC 41.5%);border-radius:16px;border:1px solid #eaebed;padding:24px}.heading{font-weight:bold;font-size:16px;margin-bottom:8px}.paragraph{font-size:15px;margin-bottom:10px;line-height:1.5}.message-box{background:#f2f2f2;border-left:4px solid #FF603E;padding:12px;font-style:italic;font-size:15px;margin-bottom:14px}.footer{font-size:13px;color:#777;text-align:center;margin-top:20px}a{color:#0867ec;text-decoration:underline}</style></head><body><table role="presentation" class="container"><tr><td class="heading">Dear ' . htmlspecialchars($name) . ',</td></tr><tr><td class="paragraph">Thank you for reaching out to <strong>Zlendo Technologies Private Limited</strong>! We’ve received your message and our team is reviewing your inquiry.</td></tr><tr><td class="paragraph">Here’s a quick summary of your message:</td></tr><tr><td class="message-box">' . nl2br(htmlspecialchars($_POST['message'])) . '</td></tr><tr><td class="paragraph">If your message requires urgent attention, please don’t hesitate to reach out at <a href="mailto:<EMAIL>"><EMAIL></a>.</td></tr><tr><td class="paragraph">Thanks again for contacting us. We look forward to assisting you!<br><br>Best regards,</td></tr><tr><td class="paragraph"><strong>Zlendo Sales Team</strong><br>Zlendo Technologies Private Limited<br><a href="mailto:<EMAIL>"><EMAIL></a><br><a href="https://www.zlendo.com">www.zlendo.com</a></td></tr><tr><td class="footer"><em>Zlendo – AI-Powered Business Solutions</em></td></tr></table></body></html>';

    $usermail->add_message($thankYouContent, '');
    $usermail->is_html = true;

    $user_result = $usermail->send();
    if ($user_result !== 'OK') {
        throw new Exception('Failed to send user confirmation');
    } else {
		file_put_contents('email_log.txt', date('Y-m-d H:i:s') . " - " . "Second Email" . ": " . $contact_result . "\n", FILE_APPEND);
	}

    $response = ['status' => 'success', 'message' => 'OK'];

} catch (Exception $e) {
    $response = ['status' => 'error', 'message' => $e->getMessage()];
}

file_put_contents('email_log.txt', date('Y-m-d H:i:s') . " - " . $response['status'] . ": " . $response['message'] . "\n", FILE_APPEND);

?>
