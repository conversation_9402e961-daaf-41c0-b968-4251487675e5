<?php
ignore_user_abort(true); // Continue even if user closes the browser
set_time_limit(0);       // Remove time limit

// Prepare early response
$response = ['status' => 'success', 'message' => 'Thank you! We received your request.'];

// Send JSON response before email
header('Content-Type: application/json');
echo json_encode($response);

// Flush response to client
if (function_exists('fastcgi_finish_request')) {
	echo "1";
    fastcgi_finish_request(); // Ends response and continues script
} else {
    echo "2";
	// Fallback for non-FPM (not guaranteed)
    ob_end_flush();
    flush();
}