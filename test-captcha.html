<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Captcha Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Captcha Test Form</h5>
                    </div>
                    <div class="card-body">
                        <form id="testForm">
                            <div class="mb-3">
                                <label for="name" class="form-label">Name</label>
                                <input type="text" id="name" name="name" class="form-control" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" id="email" name="email" class="form-control" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">Message</label>
                                <textarea id="message" name="message" class="form-control" rows="3" required></textarea>
                            </div>
                            
                            <!-- Captcha Section -->
                            <div class="mb-3">
                                <label class="form-label">Captcha</label>
                                <div class="row g-2 align-items-center">
                                    <div class="col-auto">
                                        <div id="captcha-box" class="d-flex border rounded px-3 py-2 bg-white"></div>
                                    </div>
                                    <div class="col-auto">
                                        <button type="button" onclick="cap()" class="btn btn-sm btn-warning rounded-circle">
                                            <i class="bi bi-arrow-clockwise"></i>
                                        </button>
                                    </div>
                                    <div class="col">
                                        <input type="text" id="textinput" class="form-control" placeholder="Enter captcha" required>
                                    </div>
                                </div>
                            </div>
                            
                            <input type="hidden" id="capt">
                            
                            <button type="submit" class="btn btn-primary">Submit</button>
                        </form>
                        
                        <div id="result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Generate Captcha on load
        document.addEventListener("DOMContentLoaded", cap);

        function cap() {
            const captchaBox = document.getElementById("captcha-box");
            captchaBox.innerHTML = "";
            const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            let captcha = "";

            for (let i = 0; i < 6; i++) {
                const char = chars.charAt(Math.floor(Math.random() * chars.length));
                const color = getRandomColor();
                captcha += char;
                captchaBox.innerHTML += `<span style="color:${color}; font-size: 1.25rem; font-weight: bold; margin-right: 4px;">${char}</span>`;
            }

            // Save the generated captcha value in the hidden input
            document.getElementById("capt").value = captcha;
        }

        function getRandomColor() {
            const colors = ['#FF5722', '#D84315', '#424242']; 
            return colors[Math.floor(Math.random() * colors.length)];
        }

        document.getElementById("testForm").addEventListener("submit", function (e) {
            e.preventDefault(); 
            
            const name = document.getElementById("name")?.value.trim();
            const email = document.getElementById("email")?.value.trim();
            const message = document.getElementById("message")?.value.trim();
            const userCaptcha = document.getElementById("textinput")?.value.trim();
            const generatedCaptcha = document.getElementById("capt")?.value.trim();
            
            const resultDiv = document.getElementById("result");
            resultDiv.innerHTML = "";

            // Validate required fields
            if (!name || !email || !message || !userCaptcha) {
                resultDiv.innerHTML = '<div class="alert alert-danger">Please fill in all fields.</div>';
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                resultDiv.innerHTML = '<div class="alert alert-danger">Please enter a valid email address.</div>';
                return;
            }

            // Message length validation
            if (message.length < 10) {
                resultDiv.innerHTML = '<div class="alert alert-danger">Message should be at least 10 characters.</div>';
                return;
            }

            // Captcha validation
            if (userCaptcha !== generatedCaptcha) {
                resultDiv.innerHTML = '<div class="alert alert-danger">❌ Invalid Captcha, please try again.</div>';
                document.getElementById("textinput").value = "";
                cap();
                return;
            }

            // Success
            resultDiv.innerHTML = '<div class="alert alert-success">✅ Form validation successful! Captcha verified.</div>';
            
            // Reset form
            document.getElementById("testForm").reset();
            cap(); // Generate new captcha
        });
    </script>
</body>
</html>
